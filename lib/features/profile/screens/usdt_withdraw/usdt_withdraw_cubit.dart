import 'package:bloc/bloc.dart';
import 'package:gp_stock_app/core/models/apis/deposit.dart';
import 'package:injectable/injectable.dart';

import 'usdt_withdraw_state.dart';

@injectable
class UsdtWithdrawCubit extends Cubit<UsdtWithdrawState> {
  UsdtWithdrawCubit() : super(const UsdtWithdrawState()) {
    fetchUsdtChannels();
  }

  Future<void> fetchUsdtChannels() async {
    emit(state.copyWith(isLoadingChannels: true));
    try {
      final result = await DepositApi.fetchUsdtList();
      emit(state.copyWith(
        usdtChannels: result,
        isLoadingChannels: false,
      ));
    } catch (e) {
      emit(state.copyWith(isLoadingChannels: false));
    }
  }

  void selectChannel(int channelId) {
    final selectedChannel = state.usdtChannels
        .where(
          (channel) => channel.id == channelId,
        )
        .firstOrNull;
    if (selectedChannel != null) {
      emit(state.copyWith(selectedChannel: selectedChannel));
    }
  }

  void updateAmount(String amount) {
    emit(state.copyWith(selectedAmount: amount));
  }

  void clearForm() {
    emit(state.copyWith(selectedChannel: null, selectedAmount: null));
  }
}
