import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/models/entities/deposit/usdt_channel.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/models/dropdown/dropdown_value.dart';
import 'package:gp_stock_app/shared/widgets/dropdown/common_dropdown.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

import 'usdt_withdraw_cubit.dart';
import 'usdt_withdraw_state.dart';

class UsdtWithdrawView extends StatefulWidget {
  const UsdtWithdrawView({super.key});

  @override
  State<UsdtWithdrawView> createState() => _UsdtWithdrawViewState();
}

class _UsdtWithdrawViewState extends State<UsdtWithdrawView> {
  @override
  Widget build(BuildContext context) {
    final cubit = BlocProvider.of<UsdtWithdrawCubit>(context);
    return Scaffold(
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              ShadowBox(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildChannelDropdown(context, cubit),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildChannelDropdown(BuildContext context, UsdtWithdrawCubit cubit) {
    return BlocBuilder<UsdtWithdrawCubit, UsdtWithdrawState>(
      builder: (context, state) {
        if (state.isLoadingChannels) {
          return _buildChannelDropdownShimmer();
        }

        final dropdownItems = _convertChannelsToDropdownValues(state.usdtChannels);
        final selectedItem =
            state.selectedChannel != null ? _convertChannelToDropdownValue(state.selectedChannel!) : null;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'withdrawChannel'.tr(),
              style: context.textTheme.regular.copyWith(
                fontSize: 14.gsp,
                fontWeight: FontWeight.w500,
                color: context.colorTheme.textTitle,
              ),
            ),
            SizedBox(height: 8.gh),
            CommonDropdown<DropDownValue>(
              hintText: 'pleaseSelectRechargeNetwork'.tr(),
              dropDownValue: dropdownItems,
              selectedItem: selectedItem,
              onChanged: (DropDownValue? value) {
                if (value?.code != null) {
                  cubit.selectChannel(value!.code as int);
                }
              },
              showSearchBox: false,
              height: 48.gh,
              borderRadius: 8.gr,
            ),
          ],
        );
      },
    );
  }

  Widget _buildChannelDropdownShimmer() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ShimmerWidget(
          height: 16.gh,
          width: 120.gw,
          radius: 4.gr,
        ),
        SizedBox(height: 8.gh),
        ShimmerWidget(
          height: 48.gh,
          width: double.infinity,
          radius: 8.gr,
        ),
      ],
    );
  }

  List<DropDownValue> _convertChannelsToDropdownValues(List<USDTChannel> channels) {
    return channels.map((channel) => _convertChannelToDropdownValue(channel)).toList();
  }

  DropDownValue _convertChannelToDropdownValue(USDTChannel channel) {
    final networkName = _getNetworkName(channel.network);
    return DropDownValue(
      id: channel.id.toString(),
      value: '${channel.currency} ($networkName)',
      code: channel.id,
    );
  }

  String _getNetworkName(int network) {
    switch (network) {
      case 1:
        return 'TRC20';
      case 2:
        return 'ERC20';
      case 3:
        return 'BEP20';
      default:
        return 'Unknown';
    }
  }
}
