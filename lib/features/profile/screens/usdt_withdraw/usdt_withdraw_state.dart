import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/models/entities/deposit/usdt_channel.dart';

class UsdtWithdrawState extends Equatable {
  final List<USDTChannel> usdtChannels;
  final USDTChannel? selectedChannel;
  final bool isLoadingChannels;
  final String? selectedAmount;

  const UsdtWithdrawState({
    this.usdtChannels = const [],
    this.selectedChannel,
    this.isLoadingChannels = false,
    this.selectedAmount,
  });

  UsdtWithdrawState copyWith({
    List<USDTChannel>? usdtChannels,
    USDTChannel? selectedChannel,
    bool? isLoadingChannels,
    String? selectedAmount,
  }) {
    return UsdtWithdrawState(
      usdtChannels: usdtChannels ?? this.usdtChannels,
      selectedChannel: selectedChannel ?? this.selectedChannel,
      isLoadingChannels: isLoadingChannels ?? this.isLoadingChannels,
      selectedAmount: selectedAmount ?? this.selectedAmount,
    );
  }

  @override
  List<Object?> get props => [
        usdtChannels,
        selectedChannel,
        isLoadingChannels,
        selectedAmount,
      ];
}
